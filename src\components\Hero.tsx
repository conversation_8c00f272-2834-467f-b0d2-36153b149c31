import { Download, Play, Shield } from "lucide-react";
import { useState, useEffect } from "react";
import heroImage1 from "../assets/hero-image1.png";
import heroImage2 from "../assets/hero-image2.png";
import heroImage3 from "../assets/hero-image3.png";

const Hero = () => {
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <section
      id="hero"
      className="relative flex h-screen items-center justify-center overflow-hidden"
    >
      <div
        className="absolute inset-0 z-0"
        style={{
          // backgroundImage:
          //   "url(https://images.unsplash.com/photo-1506744038136-46273834b3fb?ixlib=rb-4.0.3)",
          // backgroundSize: "cover",
          // backgroundPosition: "center",
          transform: `translateY(${scrollY * 0.5}px)`,
        }}
      >
        {/* Floating animation elements */}
        <div className="absolute top-20 left-10 w-4 h-4 bg-green-400 rounded-full animate-float opacity-60"></div>
        <div className="absolute bottom-32 right-20 w-6 h-6 bg-emerald-300 rounded-full animate-float opacity-50" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/3 right-10 w-3 h-3 bg-green-300 rounded-full animate-float opacity-70" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-20 left-1/4 w-5 h-5 bg-lime-400 rounded-full animate-float opacity-40" style={{ animationDelay: '0.5s' }}></div>
        <div className="absolute bottom-1/4 left-1/3 w-2 h-2 bg-green-500 rounded-full animate-float opacity-60" style={{ animationDelay: '1.5s' }}></div>

        {/* <div className="hero__overlay absolute inset-0"></div> */}
      </div>

      {/* Hero Content */}
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
      <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="space-y-8">
            <div className="space-y-4">
            <div className="inline-flex items-center bg-emerald-100 text-emerald-800 px-4 py-2 rounded-full text-sm font-medium">
                <Shield className="h-4 w-4 mr-2" />
                Blockchain-Powered Transparency
              </div>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-black leading-tight">
                Empowering Agriculture with{' '}
                <span className="bg-gradient-to-r from-emerald-600 to-amber-600 bg-clip-text text-transparent relative">
                  Stable Finance
                  <div className="absolute -bottom-2 left-0 w-full h-1 bg-[#2D4D31]/20 rounded-full"></div>
                </span>
              </h1>
              <p className="text-xl text-gray-600 max-w-xl">
                Bringing stability, transparency, and growth to every crop and trade.
              </p>
            </div>

            {/* Primary CTAs */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button className="flex items-center justify-center px-8 py-4 bg-[#2D4D31] text-white rounded-lg hover:bg-[#2D4D31]/90 transition-all duration-300 font-medium border-2 border-[#2D4D31] border-dotted border-opacity-70 group">
                <Download className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                View Whitepaper
              </button>
              <button 
                // onClick={() => setShowVideo(true)}
                className="flex items-center justify-center px-8 py-4 bg-white text-[#2D4D31] rounded-lg hover:bg-gray-50 transition-all duration-300 font-medium border-2 border-[#2D4D31] group"
              >
                <Play className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                Watch How It Works
              </button>
            </div>
          </div>

          {/* Visual */}
          <div className="relative w-full h-[28rem] lg:h-[36rem] flex items-center justify-center">
            {/* Collage of images */}
            <img
              src={heroImage1}
              alt="Hero 1"
              className="absolute left-0 top-0 h-60 object-cover rounded-xl shadow-lg rotate-[-8deg] z-20"
              style={{ boxShadow: '0 8px 32px rgba(44, 62, 80, 0.18)' }}
            />
            <img
              src={heroImage2}
              alt="Hero 2"
              className="absolute right-0 top-12 h-72 object-cover rounded-xl shadow-xl rotate-[10deg] z-10"
              style={{ boxShadow: '0 8px 32px rgba(44, 62, 80, 0.15)' }}
            />
            <img
              src={heroImage3}
              alt="Hero 3"
              className="absolute left-1/2 bottom-0 h-64 object-cover rounded-xl shadow-2xl -translate-x-1/2 rotate-2 z-30"
              style={{ boxShadow: '0 8px 32px rgba(44, 62, 80, 0.22)' }}
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;